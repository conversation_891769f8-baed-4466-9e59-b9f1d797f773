import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { createServiceClient } from '@/lib/supabase';

const supabase = createServiceClient();

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.email) {
      return NextResponse.json(
        { error: 'Unauthorized', success: false },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const status = searchParams.get('status');
    const priority = searchParams.get('priority');
    const search = searchParams.get('search');
    const sourceLanguage = searchParams.get('sourceLanguage');
    const targetLanguage = searchParams.get('targetLanguage');

    const offset = (page - 1) * limit;

    // Get the current user ID
    const userId = session.user.id;

    if (!userId) {
      return NextResponse.json(
        { error: 'User ID not found in session', success: false },
        { status: 401 }
      );
    }

    // Build the query with proper user filtering
    // Only return projects where the user is a member
    let query = supabase
      .from('projects')
      .select(`
        *,
        project_members!inner(user_id)
      `)
      .eq('project_members.user_id', userId);

    // Apply filters
    if (status) {
      query = query.eq('status', status);
    }
    if (priority) {
      query = query.eq('priority', priority);
    }
    if (sourceLanguage) {
      query = query.eq('source_language', sourceLanguage);
    }
    if (targetLanguage) {
      query = query.contains('target_languages', [targetLanguage]);
    }
    if (search) {
      query = query.or(`name.ilike.%${search}%,description.ilike.%${search}%`);
    }

    // Get total count for projects the user has access to
    const { count } = await supabase
      .from('projects')
      .select('*, project_members!inner(user_id)', { count: 'exact', head: true })
      .eq('project_members.user_id', userId);

    // Get paginated results
    const { data: projects, error } = await query
      .range(offset, offset + limit - 1)
      .order('created_at', { ascending: false });

    console.log('Projects query result:', {
      userId,
      projectsCount: projects?.length || 0,
      error,
      projects: projects?.map(p => ({ id: p.id, name: p.name })) || [],
    });

    if (error) {
      console.error('Database error:', error);
      return NextResponse.json(
        { error: 'Failed to fetch projects', success: false },
        { status: 500 }
      );
    }

    // Transform the data to match the expected format
    const transformedProjects = projects?.map(project => ({
      id: project.id,
      name: project.name,
      description: project.description,
      status: project.status,
      priority: project.priority,
      sourceLanguage: project.source_language,
      targetLanguages: project.target_languages,
      createdAt: project.created_at,
      updatedAt: project.updated_at,
      dueDate: project.due_date,
      budget: project.budget,
      spent: project.spent || 0,
      totalSegments: project.total_segments || 0,
      completedSegments: project.completed_segments || 0,
      reviewedSegments: project.reviewed_segments || 0,
      approvedSegments: project.approved_segments || 0,
      createdBy: project.created_by,
      organizationId: project.organization_id,
      teamMembers: [], // Simplified - will fetch separately if needed
      files: [], // Simplified - will fetch separately if needed
    })) || [];

    const totalPages = Math.ceil((count || 0) / limit);

    return NextResponse.json({
      success: true,
      data: {
        items: transformedProjects,
        total: count || 0,
        page,
        limit,
        totalPages,
      },
    });
  } catch (error) {
    console.error('API error:', error);
    return NextResponse.json(
      { error: 'Internal server error', success: false },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.email) {
      return NextResponse.json(
        { error: 'Unauthorized', success: false },
        { status: 401 }
      );
    }

    // With Supabase adapter, session.user.id is the users table ID
    const userId = session.user.id;

    if (!userId) {
      return NextResponse.json(
        { error: 'User ID not found in session', success: false },
        { status: 401 }
      );
    }

    console.log('Checking user existence for userId:', userId);

    // Ensure user exists - create if missing (handles race conditions)
    const { data: userExists, error: userCheckError } = await supabase
      .from('users')
      .select('id, email, name')
      .eq('id', userId)
      .single();

    console.log('User check result:', { userExists, userCheckError });

    if (userCheckError || !userExists) {
      console.log('User not found, creating user:', { userId, email: session.user.email });

      // Create the user if it doesn't exist
      const { data: newUser, error: createUserError } = await supabase
        .from('users')
        .insert({
          id: userId,
          email: session.user.email,
          name: session.user.name,
          avatar_url: session.user.image,
          email_verified: new Date().toISOString(),
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        })
        .select()
        .single();

      if (createUserError) {
        console.error('Failed to create user during project creation:', createUserError);
        return NextResponse.json(
          {
            error: 'Failed to create user. Please try logging out and logging back in.',
            details: createUserError,
            success: false
          },
          { status: 500 }
        );
      }

      console.log('User created during project creation:', newUser);
    } else {
      console.log('User verified for project creation:', userExists);
    }

    // Get user's organization (if any)
    const { data: orgMembership } = await supabase
      .from('organization_members')
      .select('organization_id')
      .eq('user_id', userId)
      .single();

    const body = await request.json();
    const {
      name,
      description,
      sourceLanguage,
      targetLanguages,
      dueDate,
      budget,
      priority,
      teamMembers,
    } = body;

    // Skip RPC and use manual approach directly for better reliability
    console.log('Using manual user creation and project creation approach...');

      // Force user creation with UPSERT
      console.log('Attempting manual user creation for userId:', userId);

      const { data: upsertedUser, error: userInsertError } = await supabase
        .from('users')
        .upsert({
          id: userId,
          email: session.user.email,
          name: session.user.name,
          avatar_url: session.user.image,
          email_verified: new Date().toISOString(),
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        }, {
          onConflict: 'id'
        })
        .select()
        .single();

      console.log('Manual user creation result:', { upsertedUser, userInsertError });

      if (userInsertError) {
        console.error('Failed to ensure user exists:', userInsertError);
        return NextResponse.json(
          {
            error: 'Failed to create user',
            details: userInsertError,
            success: false
          },
          { status: 500 }
        );
      }

      // Verify user exists after creation
      const { data: verifyUser, error: verifyError } = await supabase
        .from('users')
        .select('id, email, name')
        .eq('id', userId)
        .single();

      if (verifyError || !verifyUser) {
        console.error('User verification failed after creation:', { verifyError, verifyUser });
        return NextResponse.json(
          {
            error: 'User verification failed after creation',
            details: verifyError,
            success: false
          },
          { status: 500 }
        );
      }

      console.log('User verified after creation:', verifyUser);

      // Wait a moment for consistency
      await new Promise(resolve => setTimeout(resolve, 200));

      // Now create the project
      console.log('Attempting manual project creation with userId:', userId);

      const { data: project, error: projectError } = await supabase
        .from('projects')
        .insert({
          name,
          description,
          source_language: sourceLanguage,
          target_languages: targetLanguages,
          due_date: dueDate,
          budget,
          priority,
          status: 'draft',
          created_by: userId,
          organization_id: orgMembership?.organization_id || null,
          total_segments: 0,
          completed_segments: 0,
          reviewed_segments: 0,
          spent: 0,
        })
        .select()
        .single();

      console.log('Manual project creation result:', { project, projectError });

      if (projectError) {
        console.error('Project creation error:', projectError);
        return NextResponse.json(
          { error: 'Failed to create project', success: false },
          { status: 500 }
        );
      }

    // TODO: Add team members if provided (temporarily disabled due to foreign key constraint issues)
    // if (teamMembers && teamMembers.length > 0) {
    //   const memberInserts = teamMembers.map((member: any) => ({
    //     project_id: project.id,
    //     user_id: member.userId,
    //     role: member.role,
    //     languages: member.languages,
    //     assigned_at: new Date().toISOString(),
    //   }));

    //   const { error: membersError } = await supabase
    //     .from('project_members')
    //     .insert(memberInserts);

    //   if (membersError) {
    //     console.error('Team members creation error:', membersError);
    //     // Don't fail the entire request, just log the error
    //   }
    // }

    console.log('Skipping team members creation due to foreign key constraint issues');

    // TODO: Add the creator as a project manager (temporarily disabled due to foreign key constraint issues)
    // const { error: creatorMemberError } = await supabase
    //   .from('project_members')
    //   .insert({
    //     project_id: project.id,
    //     user_id: userId,
    //     role: 'project_manager',
    //     assigned_at: new Date().toISOString(),
    //   });

    // if (creatorMemberError) {
    //   console.error('Creator member insertion error:', creatorMemberError);
    //   // Don't fail the entire request, but log the error
    //   // The user can still access the project as the creator
    // }

    console.log('Skipping project member creation due to foreign key constraint issues');

    return NextResponse.json({
      success: true,
      data: {
        id: project.id,
        name: project.name,
        description: project.description,
        status: project.status,
        priority: project.priority,
        sourceLanguage: project.source_language,
        targetLanguages: project.target_languages,
        createdAt: project.created_at,
        updatedAt: project.updated_at,
        dueDate: project.due_date,
        budget: project.budget,
        spent: project.spent || 0,
        totalSegments: project.total_segments || 0,
        completedSegments: project.completed_segments || 0,
        reviewedSegments: project.reviewed_segments || 0,
        approvedSegments: 0,
        createdBy: project.created_by,
        organizationId: project.organization_id,
        teamMembers: [],
        files: [],
      },
      message: 'Project created successfully',
    });
  } catch (error) {
    console.error('API error:', error);
    return NextResponse.json(
      { error: 'Internal server error', success: false },
      { status: 500 }
    );
  }
}
