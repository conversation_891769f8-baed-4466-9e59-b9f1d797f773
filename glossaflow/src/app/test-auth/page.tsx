'use client';

import { useSession, signIn, signOut } from 'next-auth/react';
import { useState } from 'react';

export default function TestAuth() {
  const { data: session, status } = useSession();
  const [cleanupResult, setCleanupResult] = useState<any>(null);
  const [debugResult, setDebugResult] = useState<any>(null);

  const handleCleanup = async () => {
    try {
      const response = await fetch('/api/cleanup-users', {
        method: 'POST',
      });
      const result = await response.json();
      setCleanupResult(result);
    } catch (error) {
      console.error('Cleanup error:', error);
      setCleanupResult({ error: 'Failed to cleanup users' });
    }
  };

  const handleDebug = async () => {
    try {
      const response = await fetch('/api/debug-user');
      const result = await response.json();
      setDebugResult(result);
    } catch (error) {
      console.error('Debug error:', error);
      setDebugResult({ error: 'Failed to debug user' });
    }
  };

  const handleFixUser = async () => {
    try {
      const response = await fetch('/api/fix-user', {
        method: 'POST',
      });
      const result = await response.json();
      console.log('Fix user result:', result);
    } catch (error) {
      console.error('Fix user error:', error);
    }
  };

  if (status === 'loading') return <p>Loading...</p>;

  return (
    <div className="p-8">
      <h1 className="text-2xl font-bold mb-4">Authentication Test</h1>
      
      {session ? (
        <div className="space-y-4">
          <div className="bg-green-100 p-4 rounded">
            <h2 className="font-semibold">Signed in as:</h2>
            <p>Email: {session.user?.email}</p>
            <p>Name: {session.user?.name}</p>
            <p>ID: {session.user?.id}</p>
            <p>Image: {session.user?.image}</p>
          </div>
          
          <div className="space-x-2">
            <button 
              onClick={() => signOut()}
              className="bg-red-500 text-white px-4 py-2 rounded"
            >
              Sign out
            </button>
            <button 
              onClick={handleDebug}
              className="bg-blue-500 text-white px-4 py-2 rounded"
            >
              Debug User
            </button>
            <button 
              onClick={handleFixUser}
              className="bg-yellow-500 text-white px-4 py-2 rounded"
            >
              Fix User
            </button>
          </div>
        </div>
      ) : (
        <div className="space-y-4">
          <p>Not signed in</p>
          <button 
            onClick={() => signIn('google')}
            className="bg-blue-500 text-white px-4 py-2 rounded"
          >
            Sign in with Google
          </button>
        </div>
      )}

      <div className="mt-8">
        <button 
          onClick={handleCleanup}
          className="bg-purple-500 text-white px-4 py-2 rounded"
        >
          Cleanup Invalid Users
        </button>
        
        {cleanupResult && (
          <div className="mt-4 bg-gray-100 p-4 rounded">
            <h3 className="font-semibold">Cleanup Result:</h3>
            <pre>{JSON.stringify(cleanupResult, null, 2)}</pre>
          </div>
        )}
      </div>

      {debugResult && (
        <div className="mt-8 bg-gray-100 p-4 rounded">
          <h3 className="font-semibold">Debug Result:</h3>
          <pre>{JSON.stringify(debugResult, null, 2)}</pre>
        </div>
      )}
    </div>
  );
}
